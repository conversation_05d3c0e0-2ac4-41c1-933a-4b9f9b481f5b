#!/usr/bin/env python3
"""
Test script to demonstrate "uni" pattern detection in university email filtering.
Shows comprehensive coverage of university domains with "uni" text.
"""

import pandas as pd
from university_email_filter import UniversityEmailFilter

def test_uni_patterns():
    """Test various 'uni' patterns in university domains."""
    
    print("🎓 Testing University Email Filter - 'UNI' Pattern Detection")
    print("=" * 70)
    
    # Initialize the filter
    filter_engine = UniversityEmailFilter()
    
    # Comprehensive list of university emails with "uni" patterns
    uni_test_emails = [
        # Standard "uni" patterns
        '<EMAIL>',                    # Simple uni domain
        '<EMAIL>',                    # UK academic uni
        '<EMAIL>',                # Australian uni
        
        # European "uni-" patterns (German style)
        '<EMAIL>',                # University of Berlin
        '<EMAIL>',                 # University of Hamburg
        '<EMAIL>',              # University of Munich
        '<EMAIL>',               # University of Heidelberg
        
        # Italian universities (common "uni" prefix)
        '<EMAIL>',                   # University of Bologna
        '<EMAIL>',                  # University of Rome La Sapienza
        '<EMAIL>',               # University of Rome Tor Vergata
        '<EMAIL>',                     # University of Florence
        '<EMAIL>',                       # University of Padua
        '<EMAIL>',                      # University of Milano-Bicocca
        
        # Australian universities
        '<EMAIL>',               # University of Melbourne
        '<EMAIL>',                # University of New South Wales
        '<EMAIL>',                 # University of Queensland
        '<EMAIL>',                  # University of Sydney
        
        # Brazilian universities
        '<EMAIL>',                 # University of Campinas
        '<EMAIL>',                       # University of São Paulo
        '<EMAIL>',                   # Federal University of Rio de Janeiro
        
        # Other international "uni" patterns
        '<EMAIL>',                       # Swiss university
        '<EMAIL>',                         # Dutch university
        '<EMAIL>',                       # Norwegian university
        '<EMAIL>',                     # Swedish university
        '<EMAIL>',                    # Danish university
        
        # Complex "uni" patterns
        '<EMAIL>',           # Nested uni pattern
        '<EMAIL>',              # Research + uni + academic
        '<EMAIL>',             # College + uni + education
        
        # Non-university emails for comparison
        '<EMAIL>',                       # Regular email
        '<EMAIL>',                    # Business email
        '<EMAIL>',                # Business with "uni" (not university)
        '<EMAIL>',                # Government (municipal ≠ university)
        '<EMAIL>',                # Community organization
    ]
    
    print(f"\nTesting {len(uni_test_emails)} email addresses for university patterns...\n")
    
    # Test each email
    university_count = 0
    non_university_count = 0
    
    print(f"{'Email Address':<35} {'Status':<15} {'Pattern Type'}")
    print("-" * 70)
    
    for email in uni_test_emails:
        is_university = filter_engine.is_university_email(email)
        
        if is_university:
            status = "✅ University"
            university_count += 1
            
            # Determine which pattern matched
            if '.edu' in email:
                pattern_type = ".edu domain"
            elif '.ac.' in email:
                pattern_type = ".ac.* domain"
            elif 'uni-' in email:
                pattern_type = "uni- pattern"
            elif email.startswith('uni') or '@uni' in email:
                pattern_type = "uni* pattern"
            elif any(keyword in email for keyword in ['university', 'college', 'institute', 'research']):
                pattern_type = "keyword match"
            else:
                pattern_type = "other pattern"
        else:
            status = "❌ Not Univ"
            pattern_type = "N/A"
            non_university_count += 1
        
        print(f"{email:<35} {status:<15} {pattern_type}")
    
    # Summary statistics
    print("\n" + "=" * 70)
    print("📊 SUMMARY STATISTICS")
    print("=" * 70)
    print(f"Total emails tested: {len(uni_test_emails)}")
    print(f"University emails detected: {university_count}")
    print(f"Non-university emails: {non_university_count}")
    print(f"University detection rate: {(university_count/len(uni_test_emails)*100):.1f}%")
    
    # Show pattern breakdown
    print(f"\n🔍 PATTERN BREAKDOWN")
    print("-" * 30)
    
    pattern_counts = {}
    for email in uni_test_emails:
        if filter_engine.is_university_email(email):
            if '.edu' in email:
                pattern_counts['.edu domains'] = pattern_counts.get('.edu domains', 0) + 1
            elif '.ac.' in email:
                pattern_counts['.ac.* domains'] = pattern_counts.get('.ac.* domains', 0) + 1
            elif 'uni-' in email:
                pattern_counts['uni- patterns'] = pattern_counts.get('uni- patterns', 0) + 1
            elif '@uni' in email or email.startswith('uni'):
                pattern_counts['uni* patterns'] = pattern_counts.get('uni* patterns', 0) + 1
            else:
                pattern_counts['other patterns'] = pattern_counts.get('other patterns', 0) + 1
    
    for pattern, count in pattern_counts.items():
        print(f"{pattern}: {count}")

def test_dataframe_filtering():
    """Test filtering with a DataFrame containing various uni patterns."""
    
    print(f"\n\n🗂️  DATAFRAME FILTERING TEST")
    print("=" * 50)
    
    # Create test DataFrame
    test_data = {
        'Name': [
            'Prof. Schmidt', 'Dr. Rossi', 'Jane Smith', 'Prof. Wilson',
            'Dr. Mueller', 'John Doe', 'Prof. Silva', 'Sarah Johnson'
        ],
        'Email': [
            '<EMAIL>',      # German university
            '<EMAIL>',             # Italian university  
            '<EMAIL>',             # Not university
            '<EMAIL>',      # Australian university
            '<EMAIL>',             # Swiss university
            '<EMAIL>',           # Not university
            '<EMAIL>',           # Brazilian university
            '<EMAIL>'          # Not university
        ],
        'Institution': [
            'University of Berlin', 'University of Bologna', 'Private',
            'University of Melbourne', 'University of Zurich', 'Private',
            'University of Campinas', 'Private'
        ]
    }
    
    df = pd.DataFrame(test_data)
    
    print("Original DataFrame:")
    print(df.to_string(index=False))
    
    # Filter university emails
    filter_engine = UniversityEmailFilter()
    university_df = filter_engine.filter_university_emails(df)
    
    print(f"\nFiltered University DataFrame:")
    print(university_df.to_string(index=False))
    
    # Get statistics
    stats = filter_engine.get_filter_statistics(df)
    print(f"\nStatistics:")
    print(f"- Total records: {stats['total_emails']}")
    print(f"- University records: {stats['university_emails']}")
    print(f"- Detection rate: {stats['university_percentage']:.1f}%")

if __name__ == "__main__":
    test_uni_patterns()
    test_dataframe_filtering()
    
    print(f"\n\n🎯 CONCLUSION")
    print("=" * 50)
    print("The enhanced university email filter now detects:")
    print("✅ Standard .edu and .ac.* domains")
    print("✅ German uni- patterns (uni-berlin.de)")
    print("✅ Italian uni patterns (unibo.it, uniroma1.it)")
    print("✅ Australian uni patterns (unimelb.edu.au)")
    print("✅ Generic uni domains (uni.edu, uni.ch)")
    print("✅ Complex nested patterns")
    print("✅ Keyword-based detection")
    print("\nReady for production use! 🚀")
