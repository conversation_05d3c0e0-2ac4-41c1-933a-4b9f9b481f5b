#!/usr/bin/env python3
"""
University Email Filter - Professional Data Engineering Solution
Filters university and academic emails from datasets with comprehensive pattern matching.
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime
import argparse

# Import rich_progress for gradient progress bars
try:
    import rich_progress
    HAS_RICH = True
except ImportError:
    HAS_RICH = False
    print("Note: rich_progress not available. Using basic progress indicators.")

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

def print_status(message, status_type="info"):
    """Print status message with or without rich formatting."""
    if HAS_RICH:
        rich_progress.print_status(message, status_type)
    else:
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")

def print_header(title):
    """Print a header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    if HAS_RICH:
        rich_progress.print_status(f"\n{title}", "header")
        rich_progress.print_status(timestamp, "info")
        rich_progress.print_status("=" * 60, "info")
    else:
        print(f"\n{'='*60}")
        print(f"{title}")
        print(f"{timestamp}")
        print(f"{'='*60}")

def print_section(title):
    """Print a section header."""
    if HAS_RICH:
        rich_progress.print_status(f"\n>> {title}", "info")
        rich_progress.print_status("-" * (len(title) + 4), "info")
    else:
        print(f"\n>> {title}")
        print("-" * (len(title) + 4))

class UniversityEmailFilter:
    """Professional university email filtering class."""
    
    def __init__(self):
        """Initialize the filter with comprehensive university patterns."""
        
        # Core academic domain patterns
        self.academic_domains = [
            ".edu",      # US education domains
            ".ac.uk", ".ac.in", ".ac.za", ".ac.jp", ".ac.kr", ".ac.cn",
            ".ac.th", ".ac.id", ".ac.my", ".ac.sg", ".ac.nz", ".ac.au",
            ".edu.au", ".edu.sg", ".edu.my", ".edu.ph", ".edu.tw", ".edu.hk",
            ".edu.cn", ".edu.in", ".edu.pk", ".edu.bd", ".edu.lk", ".edu.np",
            ".edu.br", ".edu.ar", ".edu.mx", ".edu.co", ".edu.pe", ".edu.cl",
            ".edu.eg", ".edu.sa", ".edu.ae", ".edu.qa", ".edu.kw", ".edu.jo",
            ".edu.tr", ".edu.pl", ".edu.ro", ".edu.gr", ".edu.it", ".edu.es"
        ]
        
        # University/academic keywords
        self.university_keywords = [
            "university", "univ", "college", "school", "institute", "institut",
            "research", "academy", "academic", "scholar", "student",
            "campus", "faculty", "dept", "department", "uni-"
        ]
        
        # Specific university domain patterns
        self.university_patterns = [
            r"\.edu$",           # US education
            r"\.ac\.",           # Academic domains
            r"university\.",     # university.domain
            r"college\.",        # college.domain
            r"institute\.",      # institute.domain
            r"research\.",       # research.domain
            r"\.uni-",           # German/European uni- pattern
        ]
    
    def is_university_email(self, email):
        """
        Check if an email belongs to a university/academic institution.
        
        Args:
            email (str): Email address to check
            
        Returns:
            bool: True if email is from university/academic domain
        """
        if pd.isna(email):
            return False
            
        email = str(email).lower().strip()
        
        # Check specific academic domains
        for domain in self.academic_domains:
            if email.endswith(domain):
                return True
        
        # Check university keywords in domain
        for keyword in self.university_keywords:
            if keyword in email:
                return True
        
        # Check regex patterns
        for pattern in self.university_patterns:
            if re.search(pattern, email):
                return True
                
        return False
    
    def filter_university_emails(self, df, email_column='Email'):
        """
        Filter dataframe to return only university emails.
        
        Args:
            df (pd.DataFrame): Input dataframe
            email_column (str): Name of email column
            
        Returns:
            pd.DataFrame: Filtered dataframe with university emails only
        """
        if email_column not in df.columns:
            raise ValueError(f"Column '{email_column}' not found in dataframe")
        
        # Create filter mask
        university_mask = df[email_column].apply(self.is_university_email)
        
        return df[university_mask].copy()
    
    def get_filter_statistics(self, df, email_column='Email'):
        """
        Get statistics about university email filtering.
        
        Args:
            df (pd.DataFrame): Input dataframe
            email_column (str): Name of email column
            
        Returns:
            dict: Statistics about filtering results
        """
        total_emails = len(df)
        university_mask = df[email_column].apply(self.is_university_email)
        university_count = university_mask.sum()
        
        # Breakdown by domain type
        edu_count = df[email_column].str.contains('.edu', na=False).sum()
        ac_count = df[email_column].str.contains('.ac.', na=False).sum()
        
        return {
            'total_emails': total_emails,
            'university_emails': university_count,
            'non_university_emails': total_emails - university_count,
            'university_percentage': (university_count / total_emails * 100) if total_emails > 0 else 0,
            'edu_domains': edu_count,
            'ac_domains': ac_count
        }

def process_directory(directory_path, output_dir=None, email_column='Email'):
    """
    Process all CSV files in a directory to filter university emails.
    
    Args:
        directory_path (str): Path to directory containing CSV files
        output_dir (str): Output directory (default: creates 'university_filtered' subdirectory)
        email_column (str): Name of email column
    """
    print_header("University Email Filter - Data Engineering Solution")
    
    # Initialize filter
    filter_engine = UniversityEmailFilter()
    
    # Set working directory
    print_section("Setting Up Environment")
    os.chdir(directory_path)
    print_status(f"Working directory: {os.getcwd()}", "info")
    
    # Find CSV files
    print_section("Discovering CSV Files")
    csv_files = glob.glob('*.csv')
    if not csv_files:
        print_status("No CSV files found in the directory.", "error")
        return
    
    print_status(f"Found {len(csv_files)} CSV files to process", "success")
    
    # Create output directory
    if output_dir is None:
        output_dir = os.path.join(directory_path, "university_filtered")
    os.makedirs(output_dir, exist_ok=True)
    print_status(f"Output directory: {output_dir}", "info")
    
    # Process files
    print_section("Processing Files")
    
    total_stats = {
        'files_processed': 0,
        'total_records': 0,
        'university_records': 0,
        'files_with_errors': 0
    }
    
    for i, csv_file in enumerate(csv_files, 1):
        print_status(f"Processing file {i}/{len(csv_files)}: {csv_file}", "info")
        
        try:
            # Read CSV file
            df = pd.read_csv(csv_file, low_memory=False)
            
            if email_column not in df.columns:
                print_status(f"Warning: '{email_column}' column not found in {csv_file}. Skipping.", "warning")
                continue
            
            # Get statistics
            stats = filter_engine.get_filter_statistics(df, email_column)
            
            # Filter university emails
            university_df = filter_engine.filter_university_emails(df, email_column)
            
            # Save filtered data
            output_filename = f"university_{csv_file}"
            output_path = os.path.join(output_dir, output_filename)
            university_df.to_csv(output_path, encoding='utf-8-sig', index=False)
            
            # Update totals
            total_stats['files_processed'] += 1
            total_stats['total_records'] += stats['total_emails']
            total_stats['university_records'] += stats['university_emails']
            
            print_status(f"  ✓ {stats['university_emails']:,} university emails from {stats['total_emails']:,} total ({stats['university_percentage']:.1f}%)", "success")
            
        except Exception as e:
            print_status(f"Error processing {csv_file}: {str(e)}", "error")
            total_stats['files_with_errors'] += 1
    
    # Print final results
    print_header("Processing Complete!")
    print_section("Final Statistics")
    print_status(f"Files processed: {total_stats['files_processed']}", "success")
    print_status(f"Files with errors: {total_stats['files_with_errors']}", "info")
    print_status(f"Total records processed: {total_stats['total_records']:,}", "info")
    print_status(f"University emails found: {total_stats['university_records']:,}", "success")
    
    if total_stats['total_records'] > 0:
        percentage = (total_stats['university_records'] / total_stats['total_records']) * 100
        print_status(f"University email percentage: {percentage:.2f}%", "info")
    
    print_status(f"Output saved to: {output_dir}", "info")

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Filter university emails from CSV datasets')
    parser.add_argument('directory', help='Directory containing CSV files to process')
    parser.add_argument('--output', '-o', help='Output directory (default: creates university_filtered subdirectory)')
    parser.add_argument('--email-column', '-e', default='Email', help='Name of email column (default: Email)')
    parser.add_argument('--interactive', '-i', action='store_true', help='Interactive mode with prompts')
    
    args = parser.parse_args()
    
    if args.interactive or not args.directory:
        # Interactive mode
        directory = input("Enter directory path containing CSV files: ").strip()
        email_column = input("Enter email column name (default: Email): ").strip() or 'Email'
        output_dir = input("Enter output directory (optional): ").strip() or None
    else:
        directory = args.directory
        email_column = args.email_column
        output_dir = args.output
    
    if not os.path.exists(directory):
        print_status(f"Error: Directory '{directory}' does not exist.", "error")
        return
    
    process_directory(directory, output_dir, email_column)

if __name__ == "__main__":
    main()
