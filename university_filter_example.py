#!/usr/bin/env python3
"""
University Email Filter - Usage Examples
Demonstrates different ways to use the university email filtering functionality.
"""

import pandas as pd
import os
from university_email_filter import UniversityEmailFilter

def example_basic_usage():
    """Example 1: Basic usage with a sample dataset."""
    print("=== Example 1: Basic University Email Filtering ===\n")
    
    # Create sample data
    sample_data = {
        'Name': [
            '<PERSON>', '<PERSON>', 'Prof<PERSON>', 'Dr<PERSON>', '<PERSON>',
            '<PERSON>', 'Prof<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>'
        ],
        'Email': [
            '<EMAIL>',           # Not university
            '<EMAIL>',        # University (.edu)
            '<EMAIL>',                 # University (.edu)
            '<EMAIL>',                     # University (.ac.uk)
            '<EMAIL>',               # Not university
            '<EMAIL>',   # University (research + institute)
            '<EMAIL>',               # University (.ac.in)
            '<EMAIL>',               # Not university
            '<EMAIL>',        # University (.edu.mx)
            '<EMAIL>'                # Not university
        ]
    }
    
    df = pd.DataFrame(sample_data)
    print("Original dataset:")
    print(df.to_string(index=False))
    print(f"\nTotal records: {len(df)}")
    
    # Initialize filter
    filter_engine = UniversityEmailFilter()
    
    # Filter university emails
    university_df = filter_engine.filter_university_emails(df)
    
    print("\n" + "="*50)
    print("University emails only:")
    print(university_df.to_string(index=False))
    print(f"\nUniversity records: {len(university_df)}")
    
    # Get detailed statistics
    stats = filter_engine.get_filter_statistics(df)
    print(f"\nStatistics:")
    print(f"- Total emails: {stats['total_emails']}")
    print(f"- University emails: {stats['university_emails']}")
    print(f"- University percentage: {stats['university_percentage']:.1f}%")
    print(f"- .edu domains: {stats['edu_domains']}")
    print(f"- .ac. domains: {stats['ac_domains']}")

def example_custom_column():
    """Example 2: Using custom email column name."""
    print("\n\n=== Example 2: Custom Email Column Name ===\n")
    
    # Create sample data with different column name
    sample_data = {
        'Author Name': ['Dr. Smith', 'Prof. Johnson', 'Jane Doe'],
        'Contact_Email': [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>'
        ],
        'Institution': ['Stanford University', 'Oxford University', 'Private']
    }
    
    df = pd.DataFrame(sample_data)
    print("Dataset with custom email column:")
    print(df.to_string(index=False))
    
    # Filter with custom column name
    filter_engine = UniversityEmailFilter()
    university_df = filter_engine.filter_university_emails(df, email_column='Contact_Email')
    
    print("\nFiltered university emails:")
    print(university_df.to_string(index=False))

def example_individual_email_check():
    """Example 3: Check individual emails."""
    print("\n\n=== Example 3: Individual Email Checking ===\n")
    
    filter_engine = UniversityEmailFilter()
    
    test_emails = [
        '<EMAIL>',           # University
        '<EMAIL>',           # University
        '<EMAIL>',        # University
        '<EMAIL>',         # University
        '<EMAIL>',                  # Not university
        '<EMAIL>',               # Not university
        '<EMAIL>',        # Not university (gov, not edu)
        '<EMAIL>',           # University (uni- pattern)
        '<EMAIL>',          # University (uni* pattern)
        '<EMAIL>',                   # University (uni* pattern)
        '<EMAIL>',                   # University (uni. pattern)
        '<EMAIL>',          # University (uni* pattern)
    ]
    
    print("Individual email classification:")
    print("-" * 50)
    for email in test_emails:
        is_university = filter_engine.is_university_email(email)
        status = "✓ University" if is_university else "✗ Not University"
        print(f"{email:<30} {status}")

def example_batch_processing():
    """Example 4: Batch processing multiple files."""
    print("\n\n=== Example 4: Batch Processing Setup ===\n")
    
    # This example shows how to set up batch processing
    # (without actually creating files)
    
    print("To process multiple CSV files in a directory:")
    print("1. Place all CSV files in a directory")
    print("2. Run the script with directory path")
    print("3. Filtered files will be saved with 'university_' prefix")
    
    print("\nCommand line usage:")
    print("python university_email_filter.py /path/to/csv/files")
    print("python university_email_filter.py /path/to/csv/files --output /custom/output/dir")
    print("python university_email_filter.py --interactive")
    
    print("\nProgrammatic usage:")
    print("from university_email_filter import process_directory")
    print("process_directory('/path/to/csv/files')")

def example_domain_patterns():
    """Example 5: Show what domain patterns are detected."""
    print("\n\n=== Example 5: University Domain Patterns ===\n")
    
    filter_engine = UniversityEmailFilter()
    
    print("Academic domains detected:")
    print("- .edu (US education)")
    print("- .ac.* (Academic domains worldwide)")
    print("- .edu.* (Education domains by country)")
    
    print("\nKeyword patterns detected:")
    for keyword in filter_engine.university_keywords[:8]:  # Show first 8
        print(f"- {keyword}")
    print("- ... and more")
    
    print("\nExample university emails that would be detected:")
    university_examples = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',      # Melbourne University
        '<EMAIL>',               # University of Bologna
        '<EMAIL>',               # Generic uni domain
        '<EMAIL>',      # University of Rome
        '<EMAIL>',       # Berlin University
        '<EMAIL>'           # University of Campinas
    ]
    
    for email in university_examples:
        print(f"✓ {email}")

if __name__ == "__main__":
    # Run all examples
    example_basic_usage()
    example_custom_column()
    example_individual_email_check()
    example_batch_processing()
    example_domain_patterns()
    
    print("\n" + "="*60)
    print("Examples completed! You can now use the university email filter.")
    print("="*60)
