Starting master-s_2.0.py at 08-09-2025 12:38:26.24 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.32s/it]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.32s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00,  1.12it/s]
Starting: 100%|##########| 1/1 [00:00<00:00,  1.12it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.75s/it]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.75s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:24<00:00, 24.00s/it]
Processing: 100%|##########| 1/1 [00:24<00:00, 24.00s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.76it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.76it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.25s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.25s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 11.89it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:39<00:00, 39.32s/it]
Processing: 100%|##########| 1/1 [00:39<00:00, 39.32s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:47<00:00, 47.20s/it]
Finishing: 100%|##########| 1/1 [00:47<00:00, 47.20s/it]
SUCCESS: master-s_2.0.py completed successfully at 08-09-2025 12:40:38.59 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 08-09-2025 12:40:58.66 
